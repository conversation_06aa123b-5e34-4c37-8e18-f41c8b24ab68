# 短链接轮询系统 - MVP版本需求文档

## 系统概述

短链接轮询系统是一个基于URL轮询跳转的短链接管理系统，核心功能是通过轮询机制将用户访问的短链接动态跳转到不同的目标URL，实现流量分配和负载均衡。

**核心业务流程**：
1. 用户创建短链接（生成随机码）
2. 为短链接配置轮询数据（如WhatsApp账号）
3. 用户访问短链接时，系统轮询选择目标URL并跳转
4. 记录访问数据，提供基础统计

**技术栈**：
- 前端：原生HTML + JavaScript + Bootstrap 5.3.3
- 后端：Node.js 20.18.0 + Express.js 4.18.2 + SQLite 3.45.0
- 部署：前后端分离架构

---

## 核心功能模块

### 1. 用户登录模块
**功能描述**：简单的用户认证系统
- 硬编码用户数据：`admin/123456`
- 基础登录验证
- 登录状态保持（localStorage）

### 2. 短链接管理模块
**功能描述**：短链接的创建、查看、编辑、删除

#### 2.1 短链接列表
- 显示字段：编号、标题、短链接、访问数、创建时间、操作
- 支持搜索和分页
- 操作按钮：编辑、删除、查看轮询数据

#### 2.2 创建短链接
- 必填字段：标题（2-50字符）
- 自动生成：随机码（8-10位字母数字组合）
- 生成格式：`https://short.ly/{随机码}`
- 选填字段：备注（50字符以内）

#### 2.3 编辑短链接
- 可编辑：标题、备注
- 不可编辑：随机码、短链接

### 3. 轮询数据管理模块
**功能描述**：管理每个短链接关联的轮询数据

#### 3.1 轮询数据列表
- 显示字段：编号、数据类型、数据内容、轮询次数、添加时间、操作
- 支持按短链接筛选
- 操作按钮：编辑、删除

#### 3.2 添加轮询数据
- 数据类型：WhatsApp（固定类型）
- 数据内容：WhatsApp账号（如：************）
- 轮询规则：新数据优先轮询10轮（每轮每个账号使用1次），然后合并到常规轮询

#### 3.3 批量操作
- 批量删除：支持多选删除
- 全部删除：清空当前短链接的所有轮询数据

### 4. 访问记录模块
**功能描述**：记录和查看短链接访问数据

#### 4.1 访问记录列表
- 显示字段：编号、短链接标题、访问时间、访问次数、跳转的WhatsApp账号
- 支持按短链接筛选和时间范围筛选
- 分页显示

#### 4.2 访问统计
- 总访问量
- 今日访问量

### 5. 短链接跳转模块
**功能描述**：核心的URL轮询跳转功能

#### 5.1 跳转规则
- 有效随机码：跳转到轮询选择的WhatsApp链接
- 无效随机码：跳转到Google首页
- 跳转格式：`https://api.whatsapp.com/send/?phone={WhatsApp账号}`

#### 5.2 轮询机制
- 新批次优先：最新添加的数据优先轮询10次
- 轮询顺序：按添加时间顺序
- 轮询计数：记录每个数据的使用次数

---

## 数据结构设计

### 用户数据（硬编码）
```javascript
const users = [
  { 
    id: 1, 
    username: 'admin', 
    password: '123456', 
    role: 'admin' 
  }
];
```

### 短链接数据（localStorage）
```javascript
const shortLinks = [
  {
    id: 1,
    title: 'WS客服主链接',
    randomCode: 'rJgMftSC',
    fullUrl: 'https://short.ly/rJgMftSC',
    visits: 0,
    createdAt: '2024-01-15T10:30:00Z',
    remark: '主要客服联系方式'
  }
];
```

### 轮询数据（localStorage）
```javascript
const pollingData = [
  {
    id: 1,
    shortLinkId: 1,
    type: 'WhatsApp',
    content: '************',
    pollingCount: 5,        // 已轮询轮数
    maxPollingCount: 10,    // 新批次最大轮数
    isNew: true,            // 是否为新批次
    addedAt: '2024-01-15T10:00:00Z'
  }
];
```

### 访问记录（localStorage）
```javascript
const accessRecords = [
  {
    id: 1,
    shortLinkId: 1,
    accessTime: '2024-01-15T14:30:00Z',
    visitCount: 1,
    whatsappAccount: '************'
  }
];
```

---

## 页面路由设计

### 路由结构
```
/                    -> 登录页面
/dashboard          -> 仪表板（短链接列表）
/shortlink/create   -> 创建短链接
/shortlink/edit/:id -> 编辑短链接
/polling/:id        -> 轮询数据管理
/access-records     -> 访问记录
```

### 页面文件
- `login.html` + `login.js` - 登录页面
- `dashboard.html` + `dashboard.js` - 仪表板
- `shortlink-form.html` + `shortlink-form.js` - 短链接表单
- `polling-data.html` + `polling-data.js` - 轮询数据管理
- `access-records.html` + `access-records.js` - 访问记录

---

## 核心API接口

### 1. 用户认证
```javascript
// 登录
POST /api/auth/login
{
  "username": "admin",
  "password": "123456"
}

// 响应
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token",
    "user": { "id": 1, "username": "admin" }
  }
}
```

### 2. 短链接管理
```javascript
// 获取短链接列表
GET /api/shortlinks?page=1&limit=10

// 创建短链接
POST /api/shortlinks
{
  "title": "WS客服主链接",
  "remark": "主要客服联系方式"
}

// 更新短链接
PUT /api/shortlinks/{id}
{
  "title": "更新后的标题",
  "remark": "更新后的备注"
}
```

### 3. 轮询数据管理
```javascript
// 获取轮询数据
GET /api/polling-links?shortLinkId=1

// 添加轮询数据
POST /api/polling-links
{
  "shortLinkId": 1,
  "type": "WhatsApp",
  "content": "************"
}

// 获取轮询数据（用于跳转）
GET /api/polling-links/poll/{shortLinkId}
```

### 4. 访问记录
```javascript
// 获取访问记录
GET /api/access-records?shortLinkId=1&page=1&limit=10

// 记录访问
POST /api/access-records
{
  "shortLinkId": 1,
  "visitCount": 1,
  "whatsappAccount": "************"
}
```

---

## 开发优先级（1个下午）

### 第一阶段（13:00-14:00）：项目搭建
- 创建前后端项目结构
- 配置数据库和基础中间件
- 搭建前端HTML页面和后端API框架
- 实现基础登录功能

### 第二阶段（14:00-15:00）：后端核心功能
- 实现短链接跳转API
- 实现轮询算法
- 实现短链接管理API
- 实现访问记录API

### 第三阶段（15:00-16:00）：前端管理界面
- 实现短链接列表和表单页面
- 实现轮询数据管理页面
- 实现访问记录展示页面
- 前后端API对接

### 第四阶段（16:00-17:00）：联调和优化
- 前后端联调测试
- 基础UI美化
- 部署准备和文档

---

## 技术实现要点

### 1. 轮询算法实现
```javascript
function getNextPollingData(shortLinkId) {
  const data = getPollingDataByShortLinkId(shortLinkId);
  
  // 优先选择新批次数据（按轮数轮询）
  const newBatchData = data.filter(item => item.isNew && item.pollingCount < item.maxPollingCount);
  if (newBatchData.length > 0) {
    // 按轮数顺序选择，每轮每个账号使用1次
    const currentRound = Math.min(...newBatchData.map(item => item.pollingCount));
    const currentRoundData = newBatchData.filter(item => item.pollingCount === currentRound);
    return currentRoundData[0]; // 取第一个（按添加时间顺序）
  }
  
  // 常规轮询
  const regularData = data.filter(item => !item.isNew);
  if (regularData.length > 0) {
    return regularData[Math.floor(Math.random() * regularData.length)];
  }
  
  return null;
}
```

### 2. 随机码生成
```javascript
function generateRandomCode() {
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
```

### 3. 数据存储
```javascript
// 保存数据到localStorage
function saveData(key, data) {
  localStorage.setItem(key, JSON.stringify(data));
}

// 从localStorage读取数据
function loadData(key) {
  const data = localStorage.getItem(key);
  return data ? JSON.parse(data) : [];
}
```

---

## 部署方案

### 开发环境
- 使用Vite开发服务器
- 热重载开发
- 本地数据存储

### 生产部署
- 构建静态文件
- 部署到任意静态服务器
- 或直接打开index.html文件

---

## 注意事项

1. **数据持久化**：使用localStorage存储数据，浏览器关闭后数据保留
2. **轮询逻辑**：确保轮询算法的正确性和数据一致性
3. **用户体验**：跳转响应时间控制在500ms内
4. **错误处理**：提供友好的错误提示和异常处理
5. **兼容性**：支持主流浏览器（Chrome、Firefox、Safari）

---

*本文档适用于MVP开发，专注于核心功能实现，后续可根据需求扩展更多功能模块。*

