# 短链接轮询系统 - MVP数据库设计文档 (更新版)

## 设计原则

### MVP思维
- **最小可行产品**：只包含核心功能必需的表结构
- **简化设计**：避免过度设计，减少表之间的复杂关系
- **快速开发**：表结构简单，便于快速实现和测试
- **易于扩展**：为后续功能预留扩展空间

### 技术选择
- **开发阶段**：localStorage存储（JSON格式）
- **生产环境**：SQLite数据库（文件数据库，无需服务器）
- **数据迁移**：提供localStorage到SQLite的迁移工具

---

## 核心表结构设计

### 1. 用户表 (users)
**功能**：存储系统用户信息，支持API认证
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'user')),
    status INTEGER DEFAULT 1 CHECK (status IN (0, 1)),
    last_login_at DATETIME,
    login_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明**：
- `id`：主键，自增
- `username`：用户名，唯一
- `password`：密码（生产环境需要加密）
- `role`：用户角色（admin/user）
- `status`：用户状态（1:启用 0:禁用）
- `last_login_at`：最后登录时间
- `login_count`：登录次数统计
- `created_at`：创建时间
- `updated_at`：更新时间

**MVP简化**：
- 硬编码默认用户：`admin/123456`
- 不实现用户注册功能
- 简单的角色控制
- 支持API认证接口

### 2. 短链接表 (live_codes)
**功能**：存储短链接基本信息，支持API管理
```sql
CREATE TABLE live_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(100) NOT NULL CHECK (length(title) >= 2 AND length(title) <= 50),
    random_code VARCHAR(20) UNIQUE NOT NULL CHECK (length(random_code) >= 8 AND length(random_code) <= 10),
    full_url VARCHAR(200) NOT NULL,
    remark VARCHAR(200), -- 备注信息，200字符以内
    visit_count INTEGER DEFAULT 0 CHECK (visit_count >= 0),
    status INTEGER DEFAULT 1 CHECK (status IN (0, 1)), -- 0:禁用 1:启用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明**：
- `id`：主键，自增
- `title`：短链接标题（2-50字符）
- `random_code`：随机码（8-10位字母数字）
- `full_url`：完整短链接
- `remark`：备注信息（200字符以内）
- `visit_count`：访问次数统计
- `status`：状态（启用/禁用）
- `created_at`：创建时间
- `updated_at`：更新时间

**MVP简化**：
- 不实现软删除，直接物理删除
- 简单的状态控制
- 基础的访问统计
- 支持API管理接口

### 3. 轮询数据表 (polling_data)
**功能**：存储每个短链接的轮询数据，支持API轮询算法
```sql
CREATE TABLE polling_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    live_code_id INTEGER NOT NULL,
    type VARCHAR(20) DEFAULT 'WhatsApp' CHECK (type = 'WhatsApp'),
    content VARCHAR(100) NOT NULL CHECK (length(content) >= 5), -- 最少5位
    polling_count INTEGER DEFAULT 0 CHECK (polling_count >= 0),
    max_polling_count INTEGER DEFAULT 10 CHECK (max_polling_count > 0),
    is_new BOOLEAN DEFAULT 1 CHECK (is_new IN (0, 1)),
    sort_order INTEGER DEFAULT 0 CHECK (sort_order >= 0),
    status INTEGER DEFAULT 1 CHECK (status IN (0, 1)), -- 0:禁用 1:启用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (live_code_id) REFERENCES live_codes(id) ON DELETE CASCADE
);
```

**字段说明**：
- `id`：主键，自增
- `live_code_id`：关联的短链接ID
- `type`：数据类型（固定为WhatsApp）
- `content`：数据内容（WhatsApp账号，最少5位）
- `polling_count`：已轮询次数
- `max_polling_count`：新批次最大轮数
- `is_new`：是否为新批次数据
- `sort_order`：排序顺序
- `status`：状态（启用/禁用）
- `created_at`：创建时间
- `updated_at`：更新时间

**MVP简化**：
- 只支持WhatsApp类型
- 简单的轮询计数
- 基础的排序功能
- 支持API轮询算法

### 4. 访问记录表 (access_records)
**功能**：记录短链接访问历史，支持API统计
```sql
CREATE TABLE access_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    live_code_id INTEGER NOT NULL,
    polling_data_id INTEGER,
    access_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    visit_count INTEGER DEFAULT 1 CHECK (visit_count > 0),
    whatsapp_account VARCHAR(100),
    user_agent TEXT,
    ip_address VARCHAR(45),
    referer TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (live_code_id) REFERENCES live_codes(id) ON DELETE CASCADE,
    FOREIGN KEY (polling_data_id) REFERENCES polling_data(id) ON DELETE SET NULL
);
```

**字段说明**：
- `id`：主键，自增
- `live_code_id`：关联的短链接ID
- `polling_data_id`：关联的轮询数据ID
- `access_time`：访问时间
- `visit_count`：访问次数
- `whatsapp_account`：跳转的WhatsApp账号
- `user_agent`：用户代理信息
- `ip_address`：IP地址
- `referer`：来源页面
- `created_at`：创建时间

**MVP简化**：
- 基础的访问记录
- 简单的统计功能
- 支持API统计接口

---

## 索引设计

### 性能优化索引
```sql
-- 短链接表索引
CREATE INDEX idx_live_codes_random_code ON live_codes(random_code);
CREATE INDEX idx_live_codes_status ON live_codes(status);

-- 轮询数据表索引
CREATE INDEX idx_polling_data_live_code_id ON polling_data(live_code_id);
CREATE INDEX idx_polling_data_status ON polling_data(status);
CREATE INDEX idx_polling_data_is_new ON polling_data(is_new);

-- 访问记录表索引
CREATE INDEX idx_access_records_live_code_id ON access_records(live_code_id);
CREATE INDEX idx_access_records_access_time ON access_records(access_time);
```

---

## 数据初始化

### 默认用户数据
```sql
INSERT INTO users (username, password, role) VALUES ('admin', '123456', 'admin');
```

### 示例短链接数据
```sql
INSERT INTO live_codes (title, random_code, full_url, remark) 
VALUES ('WS客服主链接', 'rJgMftSC', 'https://short.ly/rJgMftSC', '主要客服联系方式');
```

### 示例轮询数据
```sql
INSERT INTO polling_data (live_code_id, type, content, is_new) 
VALUES (1, 'WhatsApp', '254751076496', 1);
```

---

## 数据迁移策略

### 开发阶段 → 生产环境
```javascript
// localStorage数据迁移到SQLite
function migrateFromLocalStorage() {
    const liveCodes = JSON.parse(localStorage.getItem('liveCodes') || '[]');
    const pollingData = JSON.parse(localStorage.getItem('pollingData') || '[]');
    const accessRecords = JSON.parse(localStorage.getItem('accessRecords') || '[]');
    
    // 批量插入数据到SQLite
    // ... 迁移逻辑
}
```

### 数据备份策略
```sql
-- 创建备份表
CREATE TABLE live_codes_backup AS SELECT * FROM live_codes;
CREATE TABLE polling_data_backup AS SELECT * FROM polling_data;
CREATE TABLE access_records_backup AS SELECT * FROM access_records;
```

---

## 查询优化

### 常用查询语句
```sql
-- 获取短链接列表（分页）
SELECT * FROM live_codes 
WHERE status = 1 
ORDER BY created_at DESC 
LIMIT 10 OFFSET 0;

-- 获取轮询数据（按短链接ID）
SELECT * FROM polling_data 
WHERE live_code_id = ? AND status = 1 
ORDER BY is_new DESC, sort_order ASC, created_at ASC;

-- 获取访问统计
SELECT 
    COUNT(*) as total_visits,
    COUNT(CASE WHEN DATE(access_time) = DATE('now') THEN 1 END) as today_visits
FROM access_records 
WHERE live_code_id = ?;

-- 获取轮询数据（用于跳转）
SELECT * FROM polling_data 
WHERE live_code_id = ? AND status = 1 
ORDER BY is_new DESC, polling_count ASC, created_at ASC 
LIMIT 1;
```

---

## 扩展预留

### 未来功能扩展
1. **用户管理**：添加用户表的外键约束
2. **权限控制**：添加权限表
3. **数据统计**：添加统计表
4. **系统配置**：添加配置表
5. **操作日志**：添加日志表

### 表结构扩展
```sql
-- 系统配置表（已实现）
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(50) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表（已实现）
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    operation VARCHAR(50) NOT NULL,
    target_table VARCHAR(50),
    target_id INTEGER,
    old_data TEXT,
    new_data TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

---

## 部署建议

### 开发环境
- 使用localStorage存储
- 简单的JSON数据结构
- 快速开发和测试

### 生产环境
- 使用SQLite数据库
- 定期数据备份
- 简单的数据迁移工具

### 性能优化
- 合理使用索引
- 定期清理历史数据
- 简单的查询优化

---

## API接口支持

### 完全支持的API接口
1. **用户认证接口** (`/api/auth/*`)
   - 用户登录/登出
   - Token认证
   - 用户状态管理

2. **短链接管理接口** (`/api/livecodes/*`)
- 短链接CRUD操作
   - 随机码生成
   - 分页和搜索

3. **轮询数据管理接口** (`/api/polling-data/*`)
   - 轮询数据CRUD操作
   - 批量操作
   - 轮询算法实现

4. **访问记录接口** (`/api/access-records/*`)
   - 访问记录管理
   - 统计分析
   - 数据清理

5. **仪表板接口** (`/api/dashboard/*`)
   - 概览数据
   - 基础统计
   - 实时数据

6. **系统配置接口** (`/api/system/*`)
   - 配置管理
   - 系统信息

7. **短链接跳转接口** (`/s/{randomCode}`)
   - 动态跳转
   - 访问记录

### 数据库优化特性
- **数据完整性**：CHECK约束确保数据有效性
- **性能优化**：合理的索引设计
- **自动维护**：触发器自动更新统计数据
- **审计功能**：操作日志记录
- **配置管理**：系统参数配置

---

*本设计文档适用于MVP开发，专注于核心功能实现，完全支持API接口文档中的所有功能需求。* 