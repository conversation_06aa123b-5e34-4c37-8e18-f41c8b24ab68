# 短链接轮询系统 - MVP版本API接口文档

## 接口说明

### 基础信息
- **接口协议**: HTTP/HTTPS
- **数据格式**: JSON
- **编码方式**: UTF-8
- **请求方式**: RESTful API
- **认证方式**: 简单Token认证

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 状态码说明
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误码
- `1001`: 用户名或密码错误
- `1002`: 用户已被禁用
- `2001`: 短链接标题长度不符合要求（最少2字符）
- `2002`: 随机码已存在
- `2003`: 轮询数据不足
- `3001`: 轮询数据内容不能为空

---

## 1. 用户认证接口

### 1.1 用户登录
**接口地址**: `POST /api/auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "simple_token_string",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "status": 1
    }
  }
}
```

### 1.2 用户登出
**接口地址**: `POST /api/auth/logout`

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登出成功"
}
```

---

## 2. 短链接管理接口

### 2.1 获取短链接列表
**接口地址**: `GET /api/livecodes`

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10, 最大: 100)
- `search`: 搜索关键词（标题）
- `status`: 状态筛选 (1:启用 0:禁用)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "WS客服主链接",
        "randomCode": "rJgMftSC",
        "fullUrl": "https://short.ly/rJgMftSC",
        "remark": "主要客服联系方式",
        "visitCount": 1234,
        "status": 1,
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 10,
      "total": 100,
      "pages": 10
    }
  }
}
```

### 2.2 创建短链接
**接口地址**: `POST /api/livecodes`

**请求参数**:
```json
{
  "title": "WS客服主链接",
  "remark": "主要客服联系方式"
}
```

**数据验证规则**:
- `title`: 必填，最少2字符
- `remark`: 可选

**响应数据**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "title": "WS客服主链接",
    "randomCode": "rJgMftSC",
    "fullUrl": "https://short.ly/rJgMftSC",
    "remark": "主要客服联系方式",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### 2.3 更新短链接
**接口地址**: `PUT /api/livecodes/{id}`

**请求参数**:
```json
{
  "title": "更新后的标题",
  "remark": "更新后的备注"
}
```

**数据验证规则**:
- `title`: 可选，最少2字符
- `remark`: 可选

### 2.4 删除短链接
**接口地址**: `DELETE /api/livecodes/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "删除成功"
}
```

### 2.5 生成随机码
**接口地址**: `GET /api/livecodes/generate-code`

**响应数据**:
```json
{
  "code": 200,
  "message": "生成成功",
  "data": {
    "randomCode": "kL9mNpQr"
  }
}
```

### 2.6 获取短链接详情
**接口地址**: `GET /api/livecodes/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "WS客服主链接",
    "randomCode": "rJgMftSC",
    "fullUrl": "https://short.ly/rJgMftSC",
    "remark": "主要客服联系方式",
    "visitCount": 1234,
    "status": 1,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

---

## 3. 轮询数据管理接口

### 3.1 获取轮询数据列表
**接口地址**: `GET /api/polling-data`

**查询参数**:
- `liveCodeId`: 短链接ID（必填）
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `status`: 状态筛选 (1:启用 0:禁用)
- `isNew`: 是否新批次 (1:是 0:否)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "liveCodeId": 1,
        "type": "WhatsApp",
        "content": "************",
        "pollingCount": 5,
        "maxPollingCount": 10,
        "isNew": 1,
        "sortOrder": 0,
        "status": 1,
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 10,
      "total": 50,
      "pages": 5
    }
  }
}
```

### 3.2 添加轮询数据
**接口地址**: `POST /api/polling-data`

**请求参数**:
```json
{
  "liveCodeId": 1,
  "content": "************"
}
```

**数据验证规则**:
- `liveCodeId`: 必填
- `content`: 必填，非空

**响应数据**:
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "id": 1,
    "liveCodeId": 1,
    "type": "WhatsApp",
    "content": "************",
    "pollingCount": 0,
    "maxPollingCount": 10,
    "isNew": 1,
    "status": 1,
    "createdAt": "2024-01-15T10:00:00Z"
  }
}
```

### 3.3 批量添加轮询数据
**接口地址**: `POST /api/polling-data/batch`

**请求参数**:
```json
{
  "liveCodeId": 1,
  "contents": [
    "************",
    "254751076497",
    "254751076498"
  ]
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "批量添加成功",
  "data": {
    "successCount": 3,
    "failedCount": 0,
    "addedIds": [1, 2, 3]
  }
}
```

### 3.4 更新轮询数据
**接口地址**: `PUT /api/polling-data/{id}`

**请求参数**:
```json
{
  "content": "254751076499",
  "status": 1
}
```

### 3.5 删除轮询数据
**接口地址**: `DELETE /api/polling-data/{id}`

### 3.6 批量删除轮询数据
**接口地址**: `DELETE /api/polling-data/batch`

**请求参数**:
```json
{
  "ids": [1, 2, 3]
}
```

### 3.7 清空短链接轮询数据
**接口地址**: `DELETE /api/polling-data/clear/{liveCodeId}`

**响应数据**:
```json
{
  "code": 200,
  "message": "清空成功",
  "data": {
    "deletedCount": 10
  }
}
```

### 3.8 获取轮询数据（用于跳转）
**接口地址**: `GET /api/polling-data/poll/{liveCodeId}`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "content": "************",
    "fullUrl": "https://api.whatsapp.com/send/?phone=************",
    "type": "WhatsApp",
    "pollingCount": 5,
    "isNew": 1
  }
}
```

**轮询算法说明**:
1. 优先选择新批次数据（isNew=1且pollingCount < maxPollingCount）
2. 按轮数顺序选择，每轮每个账号使用1次
3. 新批次轮询完成后，合并到常规轮询
4. 常规轮询随机选择

---

## 4. 访问记录接口

### 4.1 获取访问记录列表
**接口地址**: `GET /api/access-records`

**查询参数**:
- `liveCodeId`: 短链接ID（可选）
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `startTime`: 开始时间 (格式: YYYY-MM-DD HH:mm:ss)
- `endTime`: 结束时间 (格式: YYYY-MM-DD HH:mm:ss)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "liveCodeId": 1,
        "pollingDataId": 1,
        "accessTime": "2024-01-15T14:30:00Z",
        "visitCount": 1,
        "whatsappAccount": "************",
        "userAgent": "Mozilla/5.0...",
        "ipAddress": "*************",
        "referer": "https://google.com"
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 10,
      "total": 1000,
      "pages": 100
    }
  }
}
```

### 4.2 记录访问
**接口地址**: `POST /api/access-records`

**请求参数**:
```json
{
  "liveCodeId": 1,
  "pollingDataId": 1,
  "visitCount": 1,
  "whatsappAccount": "************",
  "userAgent": "Mozilla/5.0...",
  "ipAddress": "*************",
  "referer": "https://google.com"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "记录成功",
  "data": {
    "id": 1,
    "accessTime": "2024-01-15T14:30:00Z"
  }
}
```

### 4.3 获取访问统计
**接口地址**: `GET /api/access-records/stats`

**查询参数**:
- `liveCodeId`: 短链接ID（可选）
- `period`: 统计周期 (today/yesterday/week/month)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalVisits": 5000,
    "todayVisits": 120,
    "yesterdayVisits": 110,
    "growthRate": "+9.1%",
    "hourlyStats": [
      {"hour": "00:00", "count": 50},
      {"hour": "01:00", "count": 30}
    ]
  }
}
```

### 4.4 批量删除访问记录
**接口地址**: `DELETE /api/access-records/batch`

**请求参数**:
```json
{
  "ids": [1, 2, 3]
}
```

### 4.5 清空访问记录
**接口地址**: `DELETE /api/access-records/clear`

**请求参数**:
```json
{
  "liveCodeId": 1,
  "beforeDate": "2024-01-01T00:00:00Z"
}
```

---

## 5. 仪表板接口

### 5.1 获取仪表板概览
**接口地址**: `GET /api/dashboard/overview`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "liveCodes": {
      "total": 100,
      "active": 85,
      "disabled": 15,
      "todayCreated": 5
    },
    "visits": {
      "total": 125000,
      "today": 1200,
      "yesterday": 1100,
      "growth": "+9.1%"
    },
    "pollingData": {
      "total": 500,
      "active": 450,
      "newBatch": 50
    },
    "recentActivities": [
      {
        "type": "shortlink_created",
        "message": "创建了新短链接：WS客服主链接",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 5.2 获取基础统计
**接口地址**: `GET /api/dashboard/basic-stats`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalVisits": 35000,
    "todayVisits": 1200,
    "yesterdayVisits": 1100,
    "totalShortLinks": 100,
    "activeShortLinks": 85,
    "totalPollingData": 500
  }
}
```

---

## 6. 短链接跳转接口

### 6.1 短链接跳转
**接口地址**: `GET /s/{randomCode}`

**路径参数**:
- `randomCode`: 随机码（最少8位字母数字）

**响应**:
- 有效随机码：302重定向到WhatsApp链接
- 无效随机码：302重定向到Google首页

**跳转逻辑**:
1. 根据randomCode查找短链接
2. 检查短链接状态（启用/禁用）
3. 执行轮询算法获取下一个WhatsApp账号
4. 记录访问信息
5. 重定向到WhatsApp链接

---

## 7. 系统配置接口

### 7.1 获取系统配置
**接口地址**: `GET /api/system/config`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "defaultDomain": "short.ly",
    "maxPollingCount": 10,
    "systemName": "短链接轮询系统",
    "version": "MVP v1.0"
  }
}
```

### 7.2 更新系统配置
**接口地址**: `PUT /api/system/config`

**请求参数**:
```json
{
  "defaultDomain": "short.ly",
  "maxPollingCount": 10
}
```

---

## 请求头说明

所有需要认证的接口都需要在请求头中携带Token:
```
Authorization: Bearer {token}
Content-Type: application/json
```

## 分页参数说明

- `page`: 页码，从1开始
- `limit`: 每页数量，默认10，最大100
- 响应中包含完整的分页信息

## 时间格式说明

- 所有时间字段使用ISO 8601格式: `YYYY-MM-DDTHH:mm:ssZ`
- 时区统一使用UTC

## 状态值说明

### 用户状态
- `1`: 启用
- `0`: 禁用

### 短链接状态
- `1`: 启用
- `0`: 禁用

### 轮询数据状态
- `1`: 启用
- `0`: 禁用

### 新批次标识
- `1`: 新批次（优先轮询）
- `0`: 常规轮询

---

## 数据验证规则

### 短链接标题
- 必填
- 长度：最少2字符
- 支持中文、英文、数字、特殊字符

### 随机码
- 自动生成
- 长度：最少8位
- 字符：字母和数字组合
- 唯一性校验

### WhatsApp账号
- 必填
- 长度：最少5位
- 示例：************

---

*文档版本: MVP v1.0*  
*最后更新: 2024年1月*  
*适用环境: 开发阶段(localStorage) / 生产环境(SQLite)* 