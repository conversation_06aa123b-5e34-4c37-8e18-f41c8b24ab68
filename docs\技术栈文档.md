# 短链接轮询系统 - MVP技术栈规范

## 一、前端技术栈

### 核心框架
- **原生HTML + JavaScript** (不使用任何前端框架，简化开发)
- 原生JavaScript ES6+ (不使用TypeScript，保持简单)
- 模块化HTML页面结构

### UI方案
- **原生HTML + CSS** (不使用复杂组件库)
- 基础CSS框架：Bootstrap 5.3.3 (CDN引入，快速搭建)
- 简单的响应式布局
- 基础表单样式

### 状态管理
- 原生JavaScript对象和变量 (不使用复杂状态管理)
- localStorage进行数据持久化
- 简单的全局变量管理
- 页面间通信使用localStorage和URL参数

### 路由管理
- 原生JavaScript路由 (基于hash或history API)
- 简单的页面切换逻辑
- 基础的路由守卫功能

### 网络请求
- Fetch API (不使用Axios)
- 简单的错误处理

### 工具库
- 最小化依赖
- 原生JavaScript实现基础功能
- 必要时使用CDN引入工具库

### 构建工具
- **无需构建工具** (直接使用原生HTML/JS/CSS)
- 开发时直接在浏览器中运行
- 生产环境直接部署静态文件

### 部署方案
- **前端**: 静态文件部署 (GitHub Pages、Vercel等)
- **后端**: Node.js服务器 + SQLite数据库
- **架构**: 前后端分离

## 二、后端技术栈

### 核心框架
- Node.js 20.18.0 LTS
- Express.js 4.18.2
- SQLite 3.45.0 (文件数据库)
- better-sqlite3 4.8.1 (SQLite驱动)

### API架构
- RESTful API设计
- 前后端分离
- JSON数据格式
- 基础中间件 (CORS, body-parser等)

### 核心API接口

#### 1. 短链接跳转
```javascript
// 处理短链接访问和轮询跳转
GET /:shortCode
// 例如: GET /rJgMftSC
// 返回: 302重定向到WhatsApp链接
```

#### 2. 短链接管理
```javascript
// 获取短链接列表
GET /api/livecodes

// 创建短链接
POST /api/livecodes
{
  "title": "WS客服主链接",
  "remark": "主要客服联系方式"
}

// 更新短链接
PUT /api/livecodes/:id

// 删除短链接
DELETE /api/livecodes/:id
```

#### 3. 轮询数据管理
```javascript
// 获取轮询数据
GET /api/polling-links?liveCodeId=1

// 添加轮询数据
POST /api/polling-links
{
  "liveCodeId": 1,
  "type": "WhatsApp",
  "content": "************"
}

// 获取轮询数据（用于跳转）
GET /api/polling-links/poll/:liveCodeId
```

#### 4. 访问记录
```javascript
// 记录访问
POST /api/access-records
{
  "liveCodeId": 1,
  "visitCount": 1,
  "whatsappAccount": "************"
}

// 获取访问记录
GET /api/access-records?liveCodeId=1&page=1&limit=10
```

### 数据操作
- 基础的CRUD操作
- 简单的数据验证

### 数据库表结构
```sql
-- 用户表
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  role TEXT DEFAULT 'admin',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 短链接表
CREATE TABLE live_codes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  random_code TEXT UNIQUE NOT NULL,
  full_url TEXT NOT NULL,
  visit_count INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 轮询数据表
CREATE TABLE polling_data (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  live_code_id INTEGER NOT NULL,
  type TEXT DEFAULT 'WhatsApp',
  content TEXT NOT NULL,
  polling_count INTEGER DEFAULT 0,
  max_polling_count INTEGER DEFAULT 10,
  is_new BOOLEAN DEFAULT 1,
  added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (live_code_id) REFERENCES live_codes(id)
);

-- 访问记录表
CREATE TABLE access_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  live_code_id INTEGER NOT NULL,
  access_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  visit_count INTEGER DEFAULT 1,
  whatsapp_account TEXT,
  FOREIGN KEY (live_code_id) REFERENCES live_codes(id)
);
```

## 三、UI设计规范 (简化版)

### 设计原则
- 功能优先，美观其次
- 使用Bootstrap默认样式
- 简单的颜色搭配
- 基础的响应式设计

### 颜色系统 (简化)
- 主色调：Bootstrap默认蓝色
- 辅助色：Bootstrap默认灰色系
- 功能色：Bootstrap默认状态色

### 组件使用规范

#### 导航与布局
- Bootstrap Navbar组件
- 简单的侧边栏 (可选)
- 基础的响应式菜单
- 原生JavaScript控制页面切换

#### 表格与数据展示
- Bootstrap Table组件
- 简单的分页 (原生JavaScript实现)
- 基础的数据展示
- 原生JavaScript数据绑定

#### 表单与输入
- Bootstrap Form组件
- 原生HTML表单元素
- 原生JavaScript客户端验证

#### 对话框与提示
- Bootstrap Modal组件
- 简单的Alert提示
- 基础的确认对话框
- 原生JavaScript事件处理

#### 按钮与操作项
- Bootstrap Button组件
- 简单的下拉菜单
- 基础的批量操作
- 原生JavaScript事件绑定

### 响应式设计
- Bootstrap默认断点
- 简单的移动端适配
- 基础的表格响应式

## 四、开发规范 (简化版)

### 页面开发
- 简单的HTML页面结构
- 基础的命名规范
- 模块化JavaScript文件组织

### 代码风格
- 基础的代码格式化
- 简单的注释
- 可读性优先
- 原生JavaScript ES6+语法

### 提交规范
- 简单的提交信息
- 基础的功能测试

## 五、性能优化 (简化版)

- 基础的代码分割
- 简单的缓存策略
- 最小化资源加载

## 六、安全规范 (基础版)

- 基础的输入验证
- 简单的XSS防护
- 基础的权限控制

## 七、MVP功能范围

### 核心功能
1. 用户登录/注册 (基础认证)
2. 短链接创建和管理
3. 短链接访问统计 (基础数据)
4. 简单的用户管理

### 简化功能
- 移除复杂的权限系统
- 简化数据统计
- 基础的主题切换
- 简单的批量操作

### 技术简化
- 不使用TypeScript
- 不使用前端框架 (Vue/React/Angular)
- 不使用复杂状态管理
- 不使用高级UI组件
- 不使用构建工具 (Webpack/Vite等)
- 不使用高级部署方案

## 八、开发优先级 (极速MVP - 1个下午)

### 核心功能 (必须完成)
1. 用户登录 (硬编码用户数据)
2. 短链接增删改查
3. 轮询数据管理
4. 访问次数记录
5. 基础的数据存储 (localStorage)

### 可选功能 (时间允许时)
1. 基础的UI美化
2. 短链接编辑功能

### 开发策略
- 使用现成的模板快速搭建
- 硬编码数据，不依赖复杂数据库
- 优先实现核心业务流程
- UI使用Bootstrap默认样式，不做定制

## 九、技术债务管理

- 明确标记需要重构的代码
- 保持代码的可维护性
- 为后续优化预留接口
- 文档化已知问题

## 十、极速开发指南

### 开发时间安排 (1个下午)
- **13:00-14:00**: 项目搭建和基础框架
- **14:00-15:00**: 后端API开发（短链接跳转、轮询算法）
- **15:00-16:00**: 前端管理界面（短链接管理、数据展示）
- **16:00-17:00**: 前后端联调和基础UI

### 开发技巧
- 使用现成的HTML模板快速开始
- 前后端并行开发，先完成核心API
- 优先实现短链接跳转功能
- 使用Bootstrap CDN，无需本地安装
- 直接在浏览器中开发和测试

### 开发优先级
- 后端API优先：短链接跳转、轮询算法
- 前端管理界面：短链接管理、数据展示
- 数据库设计：SQLite表结构

### 版本选择原则
- 选择稳定版本，避免兼容性问题
- 所有版本锁定，避免开发过程中的版本变更
- MVP阶段不进行版本更新 